<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点导航 - Demo Sites Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 210, 211, 0.08) 0%, transparent 50%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
            padding: 0;
            overflow-x: hidden;
            position: relative;
            color: #1e293b;
        }



        /* 导航栏样式 */
        .nav-header {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 1px 0 rgba(255, 255, 255, 0.5) inset;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
            color: #1e293b;
        }

        .logo-icon {
            font-size: 28px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            padding: 4px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .logo-text {
            font-size: 18px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            gap: 8px;
        }

        .nav-link {
            color: #64748b;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 12px;
            position: relative;
        }

        .nav-link:hover {
            color: #1e293b;
            background: rgba(255, 255, 255, 0.6);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 120px 20px 60px;
            position: relative;
        }

        /* 装饰性元素 */
        .container::before {
            content: '';
            position: absolute;
            top: 200px;
            left: -100px;
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 50%;
            filter: blur(40px);
            animation: float 6s ease-in-out infinite;
        }

        .container::after {
            content: '';
            position: absolute;
            bottom: 200px;
            right: -100px;
            width: 150px;
            height: 150px;
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 36, 0.1));
            border-radius: 50%;
            filter: blur(30px);
            animation: float 8s ease-in-out infinite reverse;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }



        .demo-card {
            animation: slideInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) both;
        }

        .demo-icon:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            padding: 80px 40px 60px;
            background:
                linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%),
                linear-gradient(45deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 32px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 8px 32px rgba(102, 126, 234, 0.1),
                0 1px 0 rgba(255, 255, 255, 0.8) inset;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
        }

        .header h1 {
            font-size: 3.8rem;
            font-weight: 800;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: 1.1;
        }

        .header p {
            font-size: 1.3rem;
            color: #64748b;
            font-weight: 400;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 40px;
            margin-top: 60px;
            position: relative;
        }

        /* 为每个卡片添加不同的延迟动画 */
        .demo-card:nth-child(1) { animation-delay: 0.1s; }
        .demo-card:nth-child(2) { animation-delay: 0.2s; }
        .demo-card:nth-child(3) { animation-delay: 0.3s; }
        .demo-card:nth-child(4) { animation-delay: 0.4s; }

        .demo-card {
            position: relative;
            width: 100%;
            height: 420px;
            perspective: 1200px;
            cursor: pointer;
            transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            transform-style: preserve-3d;
            will-change: transform;
        }



        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }

        .demo-card.flipped .card-inner {
            transform: rotateY(180deg);
        }

        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%),
                linear-gradient(45deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 24px;
            padding: 32px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.08),
                0 8px 32px rgba(0, 0, 0, 0.04),
                0 1px 0 rgba(255, 255, 255, 0.8) inset;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            box-sizing: border-box;
            overflow: hidden;
            backdrop-filter: blur(20px);
            transition: box-shadow 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            will-change: box-shadow;
        }

        .card-front {
            transform: rotateY(0deg);
        }

        .card-back {
            transform: rotateY(180deg);
            background:
                linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%),
                linear-gradient(45deg, #f8fafc 0%, #ffffff 100%);
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .demo-card:hover::before {
            left: 100%;
        }

        .demo-card:hover {
            transform: translateY(-8px);
        }

        .demo-card:hover .card-front,
        .demo-card:hover .card-back {
            box-shadow:
                0 24px 60px rgba(0, 0, 0, 0.1),
                0 12px 30px rgba(102, 126, 234, 0.06),
                0 1px 0 rgba(255, 255, 255, 0.9) inset;
        }

        .demo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
            transform: translateZ(20px);
        }

        .demo-card:nth-child(2n) .demo-icon {
            background: linear-gradient(135deg, #4834d4, #686de0);
            box-shadow: 0 10px 20px rgba(72, 52, 212, 0.3);
        }

        .demo-card:nth-child(3n) .demo-icon {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            box-shadow: 0 10px 20px rgba(0, 210, 211, 0.3);
        }

        .demo-card:nth-child(4n) .demo-icon {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            box-shadow: 0 10px 20px rgba(95, 39, 205, 0.3);
        }

        .demo-title {
            font-size: 1.5rem;
            color: #1e293b;
            margin-bottom: 12px;
            font-weight: 700;
            flex-shrink: 0;
            letter-spacing: -0.01em;
        }

        .demo-description {
            color: #64748b;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 24px;
            text-align: center;
            flex-shrink: 0;
            font-weight: 400;
        }

        .demo-url {
            color: #888;
            font-size: 0.85rem;
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .visit-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .visit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .nav-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.12);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255,255,255,0.25);
            padding: 15px 0;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 20px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
            flex-grow: 1;
        }

        .info-section {
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
            padding: 18px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.04),
                0 1px 0 rgba(255, 255, 255, 0.8) inset;
            backdrop-filter: blur(10px);
        }

        .info-title {
            color: #1e293b;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .info-content {
            color: #64748b;
            font-size: 0.85rem;
            line-height: 1.5;
            font-weight: 400;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            margin: 10px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: #666;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .login-info {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .demo-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: auto;
            flex-shrink: 0;
        }

        .action-btn {
            padding: 10px 20px;
            border-radius: 12px;
            border: none;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-block;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 3px 10px rgba(238, 90, 36, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #4834d4, #686de0);
            color: white;
            box-shadow: 0 3px 10px rgba(72, 52, 212, 0.4);
        }

        .action-btn:hover {
            transform: translateY(-2px) scale(1.02);
        }

        .btn-primary:hover {
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.5);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(72, 52, 212, 0.5);
        }

        .btn-changelog:hover {
            box-shadow: 0 8px 25px rgba(0, 210, 211, 0.5);
        }

        .btn-features:hover {
            box-shadow: 0 8px 25px rgba(95, 39, 205, 0.5);
        }

        .btn-changelog {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            color: white;
            box-shadow: 0 3px 10px rgba(0, 210, 211, 0.4);
        }

        .btn-features {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            color: white;
            box-shadow: 0 3px 10px rgba(95, 39, 205, 0.4);
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .features-list li {
            padding: 12px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            color: #333;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li::before {
            content: "✨";
            font-size: 18px;
        }

        .back-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0,0,0,0.1);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            color: #666;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.2);
            transform: scale(1.1);
        }



        @media (max-width: 768px) {
            .nav-header {
                top: 10px;
                left: 10px;
                right: 10px;
            }

            .nav-container {
                padding: 12px 16px;
                flex-direction: column;
                gap: 16px;
            }

            .nav-links {
                gap: 8px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-link {
                padding: 6px 12px;
                font-size: 13px;
            }

            .container {
                padding: 140px 16px 40px;
            }

            .header {
                padding: 60px 24px 40px;
                margin-bottom: 60px;
            }

            .header h1 {
                font-size: 2.8rem;
            }

            .header p {
                font-size: 1.1rem;
            }

            .demo-grid {
                grid-template-columns: 1fr;
                gap: 24px;
                margin-top: 40px;
            }

            .demo-card {
                height: 380px;
            }

            .card-front, .card-back {
                padding: 24px;
            }

            .demo-info {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .demo-actions {
                flex-direction: column;
                gap: 8px;
            }

            .action-btn {
                padding: 12px 20px;
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="nav-header">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-icon">🏢</span>
                <span class="logo-text">您的企业名称</span>
            </div>
            <div class="nav-links">
                <a href="#" class="nav-link">🌐 官网首页</a>
                <a href="#" class="nav-link">💬 技术论坛</a>
                <a href="#" class="nav-link">📄 帮助文档</a>
                <a href="#" class="nav-link">📞 联系我们</a>
            </div>
        </div>
    </nav>

    <div class="container" style="padding-top: 100px;">
        <div class="header">
            <h1>演示站点导航</h1>
            <p>Demo Sites Portal - 点击下方卡片访问各个演示站点</p>
        </div>

        <div class="demo-grid" id="demoGrid">
            <!-- 示例演示站点 -->
            <div class="demo-card" onclick="flipCard(this)">
                <div class="card-inner">
                    <!-- 卡片正面 -->
                    <div class="card-front">
                        <div class="demo-icon">🚀</div>
                        <h3 class="demo-title">电商管理系统</h3>
                        <p class="demo-description">功能完整的电商管理平台，包含商品管理、订单处理、用户管理等核心功能。</p>

                        <div class="demo-info">
                            <div class="info-section">
                                <div class="info-title">🔐 后台登录</div>
                                <div class="info-content login-info">
                                    账号: admin<br>
                                    密码: 123456
                                </div>
                            </div>
                            <div class="info-section">
                                <div class="info-title">📱 小程序码</div>
                                <div class="qr-code">
                                    扫码体验<br>小程序端
                                </div>
                            </div>
                        </div>

                        <div class="demo-actions">
                            <a href="https://demo1.example.com" class="action-btn btn-primary" target="_blank" onclick="event.stopPropagation()">访问前台</a>
                            <a href="https://admin.demo1.example.com" class="action-btn btn-secondary" target="_blank" onclick="event.stopPropagation()">管理后台</a>
                            <a href="https://changelog.demo1.example.com" class="action-btn btn-changelog" target="_blank" onclick="event.stopPropagation()">更新日志</a>
                            <button class="action-btn btn-features" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">系统特色</button>
                        </div>
                    </div>

                    <!-- 卡片背面 -->
                    <div class="card-back">
                        <button class="back-btn" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">×</button>
                        <div class="demo-icon">✨</div>
                        <h3 class="demo-title">系统特色功能</h3>
                        <ul class="features-list">
                            <li>支持多商户入驻管理</li>
                            <li>完整的订单流程处理</li>
                            <li>智能库存预警系统</li>
                            <li>多种支付方式集成</li>
                            <li>数据统计分析报表</li>
                            <li>移动端完美适配</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="demo-card" onclick="flipCard(this)">
                <div class="card-inner">
                    <!-- 卡片正面 -->
                    <div class="card-front">
                        <div class="demo-icon">💼</div>
                        <h3 class="demo-title">企业OA系统</h3>
                        <p class="demo-description">企业办公自动化系统，支持审批流程、考勤管理、文档协作等功能。</p>

                        <div class="demo-info">
                            <div class="info-section">
                                <div class="info-title">🔐 测试账号</div>
                                <div class="info-content login-info">
                                    管理员: manager<br>
                                    密码: manager123<br>
                                    员工: staff001<br>
                                    密码: staff123
                                </div>
                            </div>
                            <div class="info-section">
                                <div class="info-title">🌐 访问地址</div>
                                <div class="info-content">
                                    支持PC端和移动端<br>
                                    响应式设计
                                </div>
                            </div>
                        </div>

                        <div class="demo-actions">
                            <a href="https://oa.example.com" class="action-btn btn-primary" target="_blank" onclick="event.stopPropagation()">立即体验</a>
                            <a href="https://changelog.oa.example.com" class="action-btn btn-changelog" target="_blank" onclick="event.stopPropagation()">更新日志</a>
                            <div class="action-btn btn-features" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">
                                系统特色
                            </div>
                        </div>
                    </div>

                    <!-- 卡片背面 -->
                    <div class="card-back">
                        <button class="back-btn" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">×</button>
                        <div class="demo-icon">✨</div>
                        <h3 class="demo-title">系统特色功能</h3>
                        <ul class="features-list">
                            <li>灵活的审批流程配置</li>
                            <li>多维度考勤统计</li>
                            <li>在线文档协作编辑</li>
                            <li>即时消息通讯</li>
                            <li>移动办公支持</li>
                            <li>权限精细化管理</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="demo-card" onclick="flipCard(this)">
                <div class="card-inner">
                    <!-- 卡片正面 -->
                    <div class="card-front">
                        <div class="demo-icon">🎨</div>
                        <h3 class="demo-title">在线教育平台</h3>
                        <p class="demo-description">完整的在线教育解决方案，包含课程管理、直播教学、作业系统等。</p>

                        <div class="demo-info">
                            <div class="info-section">
                                <div class="info-title">👥 角色账号</div>
                                <div class="info-content login-info">
                                    教师: teacher<br>
                                    密码: teacher123<br>
                                    学生: student<br>
                                    密码: student123
                                </div>
                            </div>
                            <div class="info-section">
                                <div class="info-title">📱 移动端</div>
                                <div class="qr-code">
                                    扫码下载<br>APP体验
                                </div>
                            </div>
                        </div>

                        <div class="demo-actions">
                            <a href="https://edu.example.com" class="action-btn btn-primary" target="_blank" onclick="event.stopPropagation()">学习平台</a>
                            <a href="https://teacher.edu.example.com" class="action-btn btn-secondary" target="_blank" onclick="event.stopPropagation()">教师端</a>
                            <a href="https://changelog.edu.example.com" class="action-btn btn-changelog" target="_blank" onclick="event.stopPropagation()">更新日志</a>
                            <div class="action-btn btn-features" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">
                                系统特色
                            </div>
                        </div>
                    </div>

                    <!-- 卡片背面 -->
                    <div class="card-back">
                        <button class="back-btn" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">×</button>
                        <div class="demo-icon">✨</div>
                        <h3 class="demo-title">系统特色功能</h3>
                        <ul class="features-list">
                            <li>高清直播互动教学</li>
                            <li>智能作业批改系统</li>
                            <li>学习进度跟踪分析</li>
                            <li>多媒体课件支持</li>
                            <li>在线考试评测</li>
                            <li>家校互动沟通</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="demo-card" onclick="flipCard(this)">
                <div class="card-inner">
                    <!-- 卡片正面 -->
                    <div class="card-front">
                        <div class="demo-icon">📊</div>
                        <h3 class="demo-title">数据分析平台</h3>
                        <p class="demo-description">企业级数据分析和可视化平台，支持多维度数据展示和智能报表生成。</p>

                        <div class="demo-info">
                            <div class="info-section">
                                <div class="info-title">🔐 演示账号</div>
                                <div class="info-content login-info">
                                    账号: demo<br>
                                    密码: demo2024
                                </div>
                            </div>
                            <div class="info-section">
                                <div class="info-title">📈 功能特色</div>
                                <div class="info-content">
                                    实时数据监控<br>
                                    自定义报表<br>
                                    数据导出
                                </div>
                            </div>
                        </div>

                        <div class="demo-actions">
                            <a href="https://analytics.example.com" class="action-btn btn-primary" target="_blank" onclick="event.stopPropagation()">查看演示</a>
                            <a href="https://changelog.analytics.example.com" class="action-btn btn-changelog" target="_blank" onclick="event.stopPropagation()">更新日志</a>
                            <div class="action-btn btn-features" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">
                                系统特色
                            </div>
                        </div>
                    </div>

                    <!-- 卡片背面 -->
                    <div class="card-back">
                        <button class="back-btn" onclick="event.stopPropagation(); flipCard(this.closest('.demo-card'))">×</button>
                        <div class="demo-icon">✨</div>
                        <h3 class="demo-title">系统特色功能</h3>
                        <ul class="features-list">
                            <li>实时数据监控大屏</li>
                            <li>多维度数据钻取</li>
                            <li>自定义报表设计</li>
                            <li>智能预警提醒</li>
                            <li>数据导出多格式</li>
                            <li>API接口开放</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 翻转卡片
        function flipCard(card) {
            card.classList.toggle('flipped');
        }

        // 复制登录信息到剪贴板
        function copyLoginInfo(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('登录信息已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('登录信息已复制到剪贴板');
            });
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                z-index: 10000;
                font-size: 14px;
                backdrop-filter: blur(10px);
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 2000);
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 为登录信息添加点击复制功能
            document.querySelectorAll('.login-info').forEach(element => {
                element.style.cursor = 'pointer';
                element.title = '点击复制登录信息';
                element.addEventListener('click', () => {
                    copyLoginInfo(element.textContent.trim());
                });
            });

            // 添加导航栏滚动效果
            let lastScrollTop = 0;
            const navbar = document.querySelector('.nav-header');

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // 向下滚动，隐藏导航栏
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // 向上滚动，显示导航栏
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
            });
        });
    </script>
</body>
</html>
