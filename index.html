<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示站点导航 - Demo Sites Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            transform: perspective(1000px) rotateX(10deg);
        }

        .header h1 {
            font-size: 3.5rem;
            color: white;
            text-shadow: 0 10px 20px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .demo-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            transform: perspective(1000px) rotateX(5deg) rotateY(-2deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.1),
                0 0 0 1px rgba(255,255,255,0.1),
                inset 0 1px 0 rgba(255,255,255,0.2);
            cursor: pointer;
            overflow: hidden;
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .demo-card:hover::before {
            left: 100%;
        }

        .demo-card:hover {
            transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(-10px) scale(1.02);
            box-shadow: 
                0 30px 60px rgba(0,0,0,0.2),
                0 0 0 1px rgba(255,255,255,0.3),
                inset 0 1px 0 rgba(255,255,255,0.3);
        }

        .demo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
            transform: translateZ(20px);
        }

        .demo-card:nth-child(2n) .demo-icon {
            background: linear-gradient(135deg, #4834d4, #686de0);
            box-shadow: 0 10px 20px rgba(72, 52, 212, 0.3);
        }

        .demo-card:nth-child(3n) .demo-icon {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            box-shadow: 0 10px 20px rgba(0, 210, 211, 0.3);
        }

        .demo-card:nth-child(4n) .demo-icon {
            background: linear-gradient(135deg, #5f27cd, #a55eea);
            box-shadow: 0 10px 20px rgba(95, 39, 205, 0.3);
        }

        .demo-title {
            font-size: 1.5rem;
            color: white;
            margin-bottom: 10px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .demo-description {
            color: rgba(255,255,255,0.8);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .demo-url {
            color: rgba(255,255,255,0.6);
            font-size: 0.85rem;
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.2);
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .visit-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .visit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        .add-demo {
            background: rgba(255,255,255,0.05);
            border: 2px dashed rgba(255,255,255,0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 280px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-demo:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.5);
        }

        .add-demo .plus-icon {
            font-size: 3rem;
            color: rgba(255,255,255,0.5);
            margin-bottom: 15px;
        }

        .add-demo .add-text {
            color: rgba(255,255,255,0.7);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .demo-card {
                transform: none;
            }
            
            .demo-card:hover {
                transform: translateY(-5px) scale(1.01);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>演示站点导航</h1>
            <p>Demo Sites Portal - 点击下方卡片访问各个演示站点</p>
        </div>

        <div class="demo-grid" id="demoGrid">
            <!-- 示例演示站点 -->
            <div class="demo-card" onclick="visitSite('https://example1.com')">
                <div class="demo-icon">🚀</div>
                <h3 class="demo-title">项目演示站 A</h3>
                <p class="demo-description">这是一个现代化的Web应用演示，展示了最新的前端技术栈和用户体验设计。</p>
                <div class="demo-url">https://example1.com</div>
                <a href="#" class="visit-btn" onclick="event.stopPropagation(); visitSite('https://example1.com')">访问站点</a>
            </div>

            <div class="demo-card" onclick="visitSite('https://example2.com')">
                <div class="demo-icon">💼</div>
                <h3 class="demo-title">企业管理系统</h3>
                <p class="demo-description">功能完整的企业级管理系统，包含用户管理、数据分析、报表生成等核心功能。</p>
                <div class="demo-url">https://example2.com</div>
                <a href="#" class="visit-btn" onclick="event.stopPropagation(); visitSite('https://example2.com')">访问站点</a>
            </div>

            <div class="demo-card" onclick="visitSite('https://example3.com')">
                <div class="demo-icon">🎨</div>
                <h3 class="demo-title">创意作品集</h3>
                <p class="demo-description">展示创意设计和开发作品的在线作品集，采用响应式设计和流畅动画。</p>
                <div class="demo-url">https://example3.com</div>
                <a href="#" class="visit-btn" onclick="event.stopPropagation(); visitSite('https://example3.com')">访问站点</a>
            </div>

            <div class="demo-card" onclick="visitSite('https://example4.com')">
                <div class="demo-icon">📱</div>
                <h3 class="demo-title">移动应用展示</h3>
                <p class="demo-description">移动端优化的应用展示页面，完美适配各种设备尺寸和操作系统。</p>
                <div class="demo-url">https://example4.com</div>
                <a href="#" class="visit-btn" onclick="event.stopPropagation(); visitSite('https://example4.com')">访问站点</a>
            </div>

            <!-- 添加新站点的卡片 -->
            <div class="demo-card add-demo" onclick="addNewDemo()">
                <div class="plus-icon">+</div>
                <div class="add-text">添加新的演示站点</div>
            </div>
        </div>
    </div>

    <script>
        function visitSite(url) {
            window.open(url, '_blank');
        }

        function addNewDemo() {
            const title = prompt('请输入演示站点标题:');
            if (!title) return;
            
            const url = prompt('请输入演示站点URL:');
            if (!url) return;
            
            const description = prompt('请输入站点描述:') || '这是一个新的演示站点';
            const icon = prompt('请输入图标 (emoji):') || '🌟';
            
            addDemoCard(title, description, url, icon);
        }

        function addDemoCard(title, description, url, icon) {
            const grid = document.getElementById('demoGrid');
            const addCard = grid.querySelector('.add-demo');
            
            const newCard = document.createElement('div');
            newCard.className = 'demo-card';
            newCard.onclick = () => visitSite(url);
            
            newCard.innerHTML = `
                <div class="demo-icon">${icon}</div>
                <h3 class="demo-title">${title}</h3>
                <p class="demo-description">${description}</p>
                <div class="demo-url">${url}</div>
                <a href="#" class="visit-btn" onclick="event.stopPropagation(); visitSite('${url}')">访问站点</a>
            `;
            
            grid.insertBefore(newCard, addCard);
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'perspective(1000px) rotateX(5deg) rotateY(-2deg)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
